// Debug script to test icon mappings
const { getMCPIconComponent } = require('./src/lib/icon-mapping.ts');

// Test data from Supabase
const templateMCP = {
  display_name: "Gmail",
  qualified_name: "gmail",
  enabled_tools: ["GMAIL_DELETE_DRAFT", "GMAIL_DELETE_MESSAGE"],
  required_config: ["client_id", "client_secret", "refresh_token"]
};

const agentMCP = {
  name: "Gmail",
  type: "http",
  config: {
    url: "https://mcp.composio.dev/composio/server/68d33e3f-af97-45ae-9a36-7870b4618dc8/mcp?user_id=03443cd2-f69a-4ff6-82d4-4e906c483c36"
  },
  app_key: "gmail",
  _metadata: {
    qualified_name: "composio/gmail",
  }
};

// Test template icon mapping
console.log("Template MCP test:");
const templateIcon = getMCPIconComponent({
  name: templateMCP.display_name,
  displayName: templateMCP.display_name,
  qualifiedName: templateMCP.qualified_name,
  isCustom: false,
  type: 'configured_mcp'
});
console.log("Template icon component:", templateIcon.displayName || templateIcon.name);

// Test agent icon mapping
console.log("\nAgent MCP test:");
const agentIcon = getMCPIconComponent({
  name: agentMCP.name,
  displayName: agentMCP.name,
  qualifiedName: agentMCP._metadata.qualified_name,
  appKey: agentMCP.app_key,
  isCustom: true,
  type: 'custom_mcp'
});
console.log("Agent icon component:", agentIcon.displayName || agentIcon.name);

// Test different qualified names
console.log("\nTesting different qualified names:");
const testCases = [
  { name: "Gmail", qualifiedName: "gmail" },
  { name: "Gmail", qualifiedName: "composio/gmail" },
  { name: "Google Sheets", qualifiedName: "google_sheets" },
  { name: "Google Sheets", qualifiedName: "composio/google_sheets" },
  { name: "Notion", qualifiedName: "notion" },
  { name: "Slack", qualifiedName: "slack" },
];

testCases.forEach(testCase => {
  const icon = getMCPIconComponent({
    name: testCase.name,
    displayName: testCase.name,
    qualifiedName: testCase.qualifiedName,
    isCustom: false,
    type: 'configured_mcp'
  });
  console.log(`${testCase.name} (${testCase.qualifiedName}):`, icon.displayName || icon.name);
});
